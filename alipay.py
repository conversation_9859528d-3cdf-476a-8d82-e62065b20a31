import ctypes
import re


# 假设 alipay.dll 是一个 32 位 DLL，需确保 Python 环境也是 32 位
# 加载 DLL
try:
    alipay_dll = ctypes.WinDLL("alipay.dll")  # 确保 alipay.dll 在工作目录或系统路径中
except OSError as e:
    print(f"Failed to load alipay.dll: {e}")
    exit(1)

# 定义 get_Alipay 函数的返回类型和参数类型
alipay_dll.get_Alipay.argtypes = [ctypes.c_char_p]  # 参数为字符串
alipay_dll.get_Alipay.restype = ctypes.c_char_p    # 返回值为字符串

def get_alipay(body: str) -> str:
    # 将 Python 字符串转换为字节字符串（C 风格字符串）
    body_bytes = body.encode('utf-8')
    
    # 调用 DLL 的 get_Alipay 函数
    result = alipay_dll.get_Alipay(body_bytes)
    
    # 将返回的 C 字符串转换为 Python 字符串
    ret = result.decode('utf-8')
    
    # 打印返回结果（对应易语言的调试输出）
    print(ret)
    
    # 提取 h5url（对应易语言的文本_取出中间文本）
    # 假设 ret 中包含类似 "js://wappay('URL')" 的内容
    pattern = r"js://wappay\('(.*?)'\)\}\,'session"
    match = re.search(pattern, ret)
    h5url = match.group(1) if match else ""
    
    # 打印 h5url（对应易语言的调试输出）
    print("h5url:", h5url)
    
    return h5url