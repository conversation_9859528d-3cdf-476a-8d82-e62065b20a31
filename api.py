"""
螃蟹支付订单创建系统 - API接口模块

提供统一的API接口，封装完整的订单创建流程
所有认证信息通过参数传递，不依赖全局配置
"""
import asyncio
import json
import time
from typing import Optional, Dict, Any
from order_service import OrderServiceFactory


async def create_order_api(
    product_id: str,
    commodity_price: str,
    j_token: str,
    j_cookie: str = "",
    sell_id: Optional[str] = None,
    config_id: Optional[str] = None,
    proxy: Optional[str] = None,
    debug: bool = False
) -> Dict[str, Any]:
    """统一的订单创建API接口
    
    这是一个统一的接口，可以执行完整的订单创建流程，包括：
    1. 获取商品销售者信息
    2. 查询保障配置
    3. 准备订单
    4. 提交订单
    
    Args:
        product_id: 商品ID
        commodity_price: 商品价格
        j_token: 认证token（必需）
        j_cookie: <PERSON>ie字符串（可选，默认为空）
        sell_id: 销售者ID（可选，自动获取）
        config_id: 保障配置ID（可选，自动获取）
        proxy: 代理服务器地址（可选）
        debug: 是否显示调试信息
        
    Returns:
        包含订单创建结果的字典:
        {
            "product_id": str,
            "steps": Dict[str, str],  # 各步骤的响应结果
            "success": bool,
            "error": Optional[str],
            "processing_time": float,  # 处理时间（秒）
            "start_time": str,  # 开始时间（ISO格式）
            "end_time": str     # 结束时间（ISO格式）
        }
        
    Example:
        # 基础用法
        result = await create_order_api(
            product_id="1418749602447905717",
            commodity_price="100.00",
            j_token="your_token_here"
        )
        
        # 带调试信息
        result = await create_order_api(
            product_id="1418749602447905717",
            commodity_price="100.00", 
            j_token="your_token_here",
            j_cookie="your_cookie_here",
            debug=True
        )
        
        # 使用自定义配置
        result = await create_order_api(
            product_id="1418749602447905717",
            commodity_price="100.00",
            j_token="your_token_here",
            j_cookie="your_cookie_here",
            proxy="http://proxy:8080"
        )
    """
    # 记录开始时间
    start_time = time.time()
    start_time_iso = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))
    
    if not j_token:
        end_time = time.time()
        end_time_iso = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(end_time))
        return {
            "product_id": product_id,
            "steps": {},
            "success": False,
            "error": "认证token不能为空，请提供j_token参数",
            "processing_time": round(end_time - start_time, 4),
            "start_time": start_time_iso,
            "end_time": end_time_iso
        }
    
    # 创建订单服务实例
    order_service = OrderServiceFactory.create_service(j_token, j_cookie, proxy)
    
    try:
        if debug:
            print(f"[{start_time_iso}] 开始创建订单 - 商品ID: {product_id}, 价格: {commodity_price}")
        
        # 执行完整的订单创建流程
        result = await order_service.create_order_complete(
            product_id=product_id,
            commodity_price=commodity_price,
            sell_id=sell_id,
            config_id=config_id,
            debug=debug
        )
        
        # 记录结束时间
        end_time = time.time()
        end_time_iso = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(end_time))
        processing_time = round(end_time - start_time, 4)
        
        if debug:
            print(f"[{end_time_iso}] 订单创建流程完成，耗时: {processing_time}秒")
            if result["success"]:
                print("✅ 订单创建成功")
            else:
                print(f"❌ 订单创建失败: {result['error']}")
        
        # 添加时间信息到结果中
        result["processing_time"] = processing_time
        result["start_time"] = start_time_iso
        result["end_time"] = end_time_iso
        
        return result
        
    except Exception as e:
        end_time = time.time()
        end_time_iso = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(end_time))
        processing_time = round(end_time - start_time, 4)
        error_msg = f"订单创建异常: {str(e)}"
        
        if debug:
            print(f"[{end_time_iso}] ❌ {error_msg}，耗时: {processing_time}秒")
        
        return {
            "product_id": product_id,
            "steps": {},
            "success": False,
            "error": error_msg,
            "processing_time": processing_time,
            "start_time": start_time_iso,
            "end_time": end_time_iso
        }


async def get_sellid(product_id: str, j_token: str, j_cookie: str = "", proxy: Optional[str] = None) -> str:
    """获取商品销售者信息
    
    Args:
        product_id: 商品ID
        j_token: 认证token（必需）
        j_cookie: Cookie字符串（可选，默认为空）
        proxy: 代理服务器地址（可选）
        
    Returns:
        API响应文本
    """
    if not j_token:
        raise ValueError("认证token不能为空，请提供j_token参数")
    
    order_service = OrderServiceFactory.create_service(j_token, j_cookie, proxy)
    return await order_service.get_sellid(product_id)


async def select_indemnity(product_id: str, commodity_price: str, j_token: str, j_cookie: str = "") -> str:
    """查询保障信息
    
    Args:
        product_id: 商品ID
        commodity_price: 商品价格
        j_token: 认证token（必需）
        j_cookie: Cookie字符串（可选，默认为空）
        
    Returns:
        API响应文本
    """
    if not j_token:
        raise ValueError("认证token不能为空，请提供j_token参数")
    
    order_service = OrderServiceFactory.create_service(j_token, j_cookie)
    return await order_service.select_indemnity(product_id, commodity_price, debug=True)


async def prepare_order(product_id: str, sell_id: str, config_id: str, j_token: str, j_cookie: str = "", proxy: Optional[str] = None) -> str:
    """准备订单
    
    Args:
        product_id: 商品ID
        sell_id: 销售者ID
        config_id: 保障配置ID
        j_token: 认证token（必需）
        j_cookie: Cookie字符串（可选，默认为空）
        proxy: 代理服务器地址（可选）
        
    Returns:
        API响应文本
    """
    if not j_token:
        raise ValueError("认证token不能为空，请提供j_token参数")
    
    order_service = OrderServiceFactory.create_service(j_token, j_cookie, proxy)
    return await order_service.prepare_order(product_id, sell_id, config_id)


async def submit_order(product_id: str, sell_id: str, config_id: str, j_token: str, j_cookie: str = "", proxy: Optional[str] = None) -> str:
    """提交订单
    
    Args:
        product_id: 商品ID
        sell_id: 销售者ID
        config_id: 保障配置ID
        j_token: 认证token（必需）
        j_cookie: Cookie字符串（可选，默认为空）
        proxy: 代理服务器地址（可选）
        
    Returns:
        API响应文本
    """
    if not j_token:
        raise ValueError("认证token不能为空，请提供j_token参数")
    
    order_service = OrderServiceFactory.create_service(j_token, j_cookie, proxy)
    return await order_service.submit_order(product_id, sell_id, config_id)