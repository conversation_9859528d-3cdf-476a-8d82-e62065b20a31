"""
订单服务模块

封装完整的订单创建业务流程
"""
import json
from typing import Dict, Any, Optional, List
from config import OrderConfig
from http_client import HttpClient, HttpClientManager
import aiohttp

class OrderService:
    """订单服务类
    
    提供完整的订单创建业务流程
    """
    
    def __init__(self, j_token: str, j_cookie: str = "", proxy: Optional[str] = None):
        """初始化订单服务
        
        Args:
            j_token: 认证token
            j_cookie: Cookie字符串
            proxy: 代理服务器地址
        """
        self.j_token = j_token
        self.j_cookie = j_cookie
        self.proxy = proxy
        self.order_config = OrderConfig()
    
    async def get_sellid(self, product_id: str) -> str:
        """获取商品销售者信息
        
        Args:
            product_id: 商品ID
            
        Returns:
            API响应文本
            
        Raises:
            aiohttp.ClientError: HTTP请求错误
        """
        async with HttpClientManager.with_client(
            self.j_token, self.j_cookie, self.proxy
        ) as client:
            return await client.get(
                "product_detail",
                product_id,
                productId=product_id,
                productType=6,
                request="params"
            )
    
    async def select_indemnity(self, product_id: str, commodity_price: str, debug: bool = False) -> str:
        """查询保障信息
        
        Args:
            product_id: 商品ID
            commodity_price: 商品价格
            debug: 是否输出调试信息
            
        Returns:
            API响应文本
            
        Raises:
            aiohttp.ClientError: HTTP请求错误
        """
        data = {
            "productId": product_id,
            "bizChannel": self.order_config.biz_channel,
            "bizLine": self.order_config.biz_line,
            "gameId": self.order_config.game_id,
            "tradeMode": self.order_config.trade_mode,
            "purchaseLimit": self.order_config.purchase_limit,
            "commodityPrice": commodity_price
        }
        
        if debug:
            print("=== 调试信息 ===")
            print("请求数据:", json.dumps(data, ensure_ascii=False, indent=2))
            print("Cookie:", self.j_cookie)
            print("Token:", self.j_token)
            print("================")
        
        async with HttpClientManager.with_client(
            self.j_token, self.j_cookie, self.proxy
        ) as client:
            return await client.post("indemnity_query", product_id, data)
    
    async def prepare_order(self, product_id: str, sell_id: str, config_id: str) -> str:
        """准备订单
        
        Args:
            product_id: 商品ID
            sell_id: 销售者ID
            config_id: 保障配置ID
            
        Returns:
            API响应文本
            
        Raises:
            aiohttp.ClientError: HTTP请求错误
        """
        data = {
            "submitOrderReqDTOList": [{
                "productId": product_id,
                "sellerId": sell_id,
                "gameId": self.order_config.game_id,
                "productType": self.order_config.product_type,
                "productQuantity": self.order_config.product_quantity,
                "easyBuy": self.order_config.easy_buy,
                "indemnityIdList": [config_id]
            }],
            "payMode": self.order_config.pay_mode,
            "serviceMode": self.order_config.service_mode
        }
        
        async with HttpClientManager.with_client(
            self.j_token, self.j_cookie, self.proxy
        ) as client:
            return await client.post("prepare_order", product_id, data)
    
    async def submit_order(self, product_id: str, sell_id: str, config_id: str) -> str:
        """提交订单
        
        Args:
            product_id: 商品ID
            sell_id: 销售者ID
            config_id: 保障配置ID
            
        Returns:
            API响应文本
            
        Raises:
            aiohttp.ClientError: HTTP请求错误
        """
        data = {
            "submitOrderReqDTOList": [{
                "productId": product_id,
                "sellerId": sell_id,
                "gameId": self.order_config.game_id,
                "productType": self.order_config.product_type,
                "productQuantity": self.order_config.product_quantity,
                "easyBuy": self.order_config.easy_buy,
                "indemnityIdList": [config_id]
            }],
            "payMode": self.order_config.pay_mode,
            "serviceMode": self.order_config.service_mode
        }
        
        async with HttpClientManager.with_client(
            self.j_token, self.j_cookie, self.proxy
        ) as client:
            return await client.post("submit_order", product_id, data)
    
    async def get_alipay_sdk(order_id: str, j_token: str, j_cookie: str = "", proxy: Optional[str] = None) -> str:
        url = "http://client-api.pxb7.com/api/order/mobile/pay/toPay"
        data = {
            "depositId": "",
            "payType": 1,
            "assPaymentId": "",
            "bizType": 0,
            "orderId": order_id,
            "merchantId": "",
            "orderItemId": "",
            "voucherId": ""
        }
        headers = {
            "client_type": "1",
            "app_version": "6.1.7",
            "px-authorization-user": j_token,  # 假设 j_token 是全局变量或从外部传入
            "Content-Type": "application/json; charset=UTF-8",
            "Content-Length": str(len(json.dumps(data))),
            "Host": "client-api.pxb7.com",
            "Connection": "Keep-Alive",
            "User-Agent": "okhttp/4.11.0"
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data, headers=headers) as response:
                response.raise_for_status()
                return await response.text()




    async def create_order_complete(
        self, 
        product_id: str, 
        commodity_price: str,
        sell_id: Optional[str] = None,
        config_id: Optional[str] = None,
        debug: bool = False
    ) -> Dict[str, Any]:
        """完整的订单创建流程
        
        Args:
            product_id: 商品ID
            commodity_price: 商品价格
            sell_id: 销售者ID（可选，自动获取）
            config_id: 保障配置ID（可选，自动获取）
            debug: 是否输出调试信息
            
        Returns:
            包含所有步骤结果的字典
            
        Raises:
            Exception: 业务流程执行错误
        """
        results = {
            "product_id": product_id,
            "steps": {},
            "success": False,
            "error": None
        }
        
        try:
            # 步骤1: 获取sellid（如果未提供）
            if not sell_id:
                if debug:
                    print(f"步骤1: 获取商品 {product_id} 的销售者信息...")
                    
                sellid_response = await self.get_sellid(product_id)
                # print(f'sellid_response: {sellid_response}')
                # print("=" * 60)
                #$.data.sellerId
                sell_id = json.loads(sellid_response)["data"]["sellerId"]

                # 这里需要从响应中解析sellid，具体逻辑根据API响应格式调整
                # 临时返回响应，让调用者处理
                if debug:
                    print("获取销售者信息完成")
            else:
                results["data"]["sellerId"] = "已提供sellid，跳过获取步骤"
            
            # 步骤2: 选择保障配置（如果未提供）
            if not config_id:
                if debug:
                    print(f"步骤2: 查询商品 {product_id} 的保障配置...")
                    
                indemnity_response = await self.select_indemnity(product_id, commodity_price, debug)
                # print(f'indemnity_response: {indemnity_response}')
                # print("=" * 60)

                # 解析保障配置响应
                indemnity_data = json.loads(indemnity_response)

                # 检查响应结构并提取config_id
                if (indemnity_data.get("success") and
                    indemnity_data.get("data") and
                    len(indemnity_data["data"]) > 0 and
                    indemnity_data["data"][0].get("childList") and
                    len(indemnity_data["data"][0]["childList"]) > 2):

                    # 正确的路径：$.data[0].childList[2].configId
                    config_id = indemnity_data["data"][0]["childList"][2]["configId"]

                    if debug:
                        print(f"成功获取config_id: {config_id}")
                else:
                    # 如果结构不符合预期，尝试获取第一个可用的配置
                    if (indemnity_data.get("data") and
                        len(indemnity_data["data"]) > 0 and
                        indemnity_data["data"][0].get("childList") and
                        len(indemnity_data["data"][0]["childList"]) > 0):

                        config_id = indemnity_data["data"][0]["childList"][0]["configId"]
                        if debug:
                            print(f"使用第一个可用的config_id: {config_id}")
                    else:
                        raise ValueError(f"无法从保障配置响应中提取config_id，响应结构异常: {indemnity_response[:200]}...")
                
                # 这里需要从响应中解析config_id，具体逻辑根据API响应格式调整
                # 临时返回响应，让调用者处理
                if debug:
                    print("查询保障配置完成")
            else:
                results["steps"]["select_indemnity"] = "已提供config_id，跳过查询步骤"
            
            # 如果sell_id和config_id都已提供或成功获取，继续后续步骤
            if sell_id and config_id:
                # 步骤3: 准备订单
                if debug:
                    print(f"步骤3: 准备订单...")
                    
                prepare_response = await self.prepare_order(product_id, sell_id, config_id)
                # results["steps"]["prepare_order"] = prepare_response
                
                if debug:
                    print("准备订单完成")
                
                # 步骤4: 提交订单
                if debug:
                    print(f"步骤4: 提交订单...")
                
                print(f"product_id: {product_id}, sell_id: {sell_id}, config_id: {config_id}")
                submit_response = await self.submit_order(product_id, sell_id, config_id)
                # results["steps"]["submit_order"] = submit_response
                print(f'submit_response: {submit_response}')
                
                if debug:
                    print("提交订单完成")
                status = json.loads(submit_response)["status"]
                if status == True:
                    results["success"] = True
                    results["order_id"] = json.loads(submit_response)["data"]["orderId"]
                    results["alipay_url"] = await self.get_alipay_sdk(results["order_id"], self.j_token, self.j_cookie, self.proxy)
                else:
                    results["errMessage"] = json.loads(submit_response)["errMessage"]
                
            else:
                results["errMessage"] = "需要手动提供sell_id和config_id，或实现响应解析逻辑"
                
        except Exception as e:
            results["error"] = str(e)
            results["success"] = False
            
            if debug:
                print(f"订单创建失败: {str(e)}")
        
        return results


class OrderServiceFactory:
    """订单服务工厂类
    
    提供便捷的订单服务创建方法
    """
    
    @staticmethod
    def create_service(j_token: str, j_cookie: str = "", proxy: Optional[str] = None) -> OrderService:
        """创建订单服务实例
        
        Args:
            j_token: 认证token
            j_cookie: Cookie字符串
            proxy: 代理服务器地址
            
        Returns:
            配置好的订单服务实例
        """
        return OrderService(j_token, j_cookie, proxy)